.f-button {
  box-sizing: border-box;
  line-height: 30px;
  font-size: 11px;
  border-radius: 6px;
  border-style: solid;
  border-width: 1px;
  border-color: var(--f-black);
  background-color: var(--f-white);
  text-align: center;
  transition: ease-in-out .2s background-color;
}

.f-button:active {
  border-color: var(--f-primary-color);
  box-shadow: inset 0 0 0 1pxvar (--f-primary-color);
  outline: none;
}

.f-button:focus {
  outline: none;
}

.f-button.primary {
  background-color: var(--f-primary-color);
  color: var(--f-white);
  // border-style: none;
}

.f-button.warn {
  background-color: var(--f-Red);
  color: var(--f-white);
  // border-style: none;
}

.f-button.primary:active {
  box-shadow: inset 0 0 0 2px rgba(0, 0, 0, .3);
}

@keyframes spin {
  from {
    transform: translateY(-50%) rotate(0);

  }

  to {
    transform: translateY(-50%) rotate(360deg);
  }
}

button.f-button--disabled.f-button {
  background-color: rgba(0, 0, 0, .3);
  color: white;
  position: relative;
}

button.f-button--loading.f-button::before {
  content: '';
  background-image: url(./assets/loading.svg);
  background-size: 14px;
  position: absolute;
  left: 14px;
  height: 14px;
  width: 14px;
  top: 50%;
  animation: spin linear .5s infinite;
}
