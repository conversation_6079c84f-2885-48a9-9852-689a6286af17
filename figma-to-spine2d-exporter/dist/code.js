(()=>{"use strict";figma.showUI(__html__,{width:300,height:200}),console.log("[Figma Plugin] 插件已加载，UI已显示"),console.log("[Figma Plugin] 当前Figma API版本:",figma.apiVersion),console.log("[Figma Plugin] 当前文档ID:",figma.fileKey),console.log("[Figma Plugin] 当前页面:",figma.currentPage.name),console.log("[Figma Plugin] figma对象:",figma),console.log("[Figma Plugin] figma.ui对象:",figma.ui),console.log("[Figma Plugin] figma.ui.onmessage类型:",typeof figma.ui.onmessage),console.log("[Figma Plugin] figma.ui.postMessage类型:",typeof figma.ui.postMessage);try{console.log("[Figma Plugin] 尝试发送测试消息到UI"),figma.ui.postMessage({type:"test",message:"这是一条测试消息"}),console.log("[Figma Plugin] 测试消息已发送")}catch(e){console.error("[Figma Plugin] 发送测试消息时出错:",e)}figma.ui.onmessage=async e=>{if(console.log("[Figma Plugin] 收到消息:",e),"export-spine2d"===e.type)try{console.log("[Figma Plugin] 处理选择...");const e=figma.currentPage.selection;if(0===e.length)return console.log("[Figma Plugin] 未找到选择"),void figma.ui.postMessage({type:"error",message:"请选择要导出的元素"});const g=e[0];if(console.log("[Figma Plugin] 选中节点类型:",g.type,"名称:",g.name),"FRAME"===g.type){const e=g;console.log("[Figma Plugin] 开始转换Frame为JSON...");const i=await async function(e){console.log("[Figma Plugin] 开始转换Frame:",e.name);const g={name:e.name,type:e.type,width:e.width,height:e.height,elements:[]},i={x:e.x,y:e.y,width:e.width,height:e.height};console.log("[Figma Plugin] Frame边界:",i);const o=e.children;console.log(`[Figma Plugin] 处理 ${o.length} 个子节点`);for(let e=0;e<o.length;e++){const n=o[e];console.log(`[Figma Plugin] 处理节点: ${n.name} (${n.type})`);const a=n.x-i.x,s=n.y-i.y,l=a+n.width/2,t=s+n.height/2;g.elements.push({name:n.name,type:n.type,x:a,y:s,width:n.width,height:n.height,centerX:l,centerY:t})}return g}(e);console.log("[Figma Plugin] 导出完成"),figma.ui.postMessage({type:"export-complete",data:[{name:e.name,data:i}]})}else console.log("[Figma Plugin] 不支持的节点类型:",g.type),figma.ui.postMessage({type:"error",message:`目前只支持导出Frame类型，您选择的是 ${g.type} 类型。`})}catch(e){console.error("[Figma Plugin] 导出错误:",e);const g=e instanceof Error?e.message:"未知错误";figma.ui.postMessage({type:"error",message:`导出失败: ${g}`})}}})();