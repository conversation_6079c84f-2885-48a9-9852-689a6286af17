/* 所有页面通用的样式 */
:root {
  --f-primary-color: #18a0fb;
  --f-blue: #18a0fb;
  --f-purple: #f0f;
  --f-green: #1bc47d;
  --f-Red: #f24822;
  --f-yellow: #ffeb00;
  --f-white: #fff;
  --f-black: #000;
  --f-content: rgba(0, 0, 0, .8);
  --f-hover-background: rgba(24, 160, 251, .1);
}

body {
  font: 11px sans-serif;
  color: var(--f-content);
  margin: 0;
}

a {
  cursor: pointer;
  text-decoration: none;
}

input:active {
  outline: none;
}

input {
  padding: 0 8px;
}

input::placeholder {
  color: rgba(0, 0, 0, .3);
}

input[type='radio'] {
  margin: 0;
  margin-right: 5px;
  vertical-align: sub;
}

input[type='text'] {
  line-height: 32px;
  width: 100%;
  border-color: rgba(0, 0, 0, .3);
  border-radius: 2px;
  border-width: 1px;
}

input[type='text']:focus {
  outline: var(--f-primary-color) auto 1px;
}
