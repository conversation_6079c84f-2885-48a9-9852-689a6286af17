(()=>{"use strict";const e=document.getElementById("export-btn"),o=document.getElementById("status");e.addEventListener("click",(()=>{console.log("[Spine2D UI] Export button clicked"),n("正在导出...","info",!0),e.disabled=!0,console.log("[Spine2D UI] Sending export message to main code");try{const e={pluginMessage:{type:"export-spine2d"}};console.log("[Spine2D UI] Message to send:",e),parent.postMessage(e,"*"),console.log("[Spine2D UI] Message sent successfully")}catch(o){console.error("[Spine2D UI] Error sending message:",o),n("发送消息时出错","error"),e.disabled=!1}})),console.log("[Spine2D UI] UI script loaded and event listeners attached"),window.onmessage=o=>{console.log("[Spine2D UI] Received message from main code:",o.data);const t=o.data.pluginMessage||o.data;console.log("[Spine2D UI] Processed message:",t),"export-complete"===t.type?(console.log("[Spine2D UI] Export completed, processing data"),function(o){try{o.forEach((({name:e,data:o})=>{const n=JSON.stringify(o,null,2),t=new Blob([n],{type:"application/json"}),s=URL.createObjectURL(t),a=document.createElement("a");a.href=s,a.download=`${e}.json`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(s)}));const e=o.length;n(1===e?"成功导出 1 个文件":`成功导出 ${e} 个文件`,"success")}catch(e){n("下载文件时出错","error")}e.disabled=!1}(t.data)):"error"===t.type?(console.log("[Spine2D UI] Error received:",t.message),n(t.message,"error"),e.disabled=!1):console.log("[Spine2D UI] Unknown message type:",t.type)},console.log("[Spine2D UI] Window object:",typeof window,window.location?window.location.href:"no location"),console.log("[Spine2D UI] Parent object:",typeof parent,parent===window?"parent is window":"parent is not window");try{console.log("[Spine2D UI] Window parent relationship:",{"window === parent":window===parent,"window === self":window===self,"parent === self":parent===self,"window.parent === parent":window.parent===parent})}catch(e){console.error("[Spine2D UI] Error checking window relationships:",e)}function n(e,n,t=!1){o.className=`status ${n}`,o.style.display="block",t?o.innerHTML=`<span class="loading"></span>${e}`:o.textContent=e,"success"===n&&setTimeout((()=>{o.style.display="none"}),3e3)}})();