// UI code for the plugin

const exportBtn = document.getElementById('export-btn') as HTMLButtonElement;
const statusDiv = document.getElementById('status') as HTMLDivElement;

// Handle export button click
exportBtn.addEventListener('click', () => {
  console.log('[Spine2D UI] Export button clicked');
  showStatus('正在导出...', 'info', true);
  exportBtn.disabled = true;

  console.log('[Spine2D UI] Sending export message to main code');
  // Send message to main plugin code
  try {
    const message = {
      pluginMessage: {
        type: 'export-spine2d'
      }
    };
    console.log('[Spine2D UI] Message to send:', message);
    parent.postMessage(message, '*');
    console.log('[Spine2D UI] Message sent successfully');
  } catch (error) {
    console.error('[Spine2D UI] Error sending message:', error);
    showStatus('发送消息时出错', 'error');
    exportBtn.disabled = false;
  }
});

console.log('[Spine2D UI] UI script loaded and event listeners attached');

// Handle messages from main plugin code
window.onmessage = (event) => {
  console.log('[Spine2D UI] Received message from main code:', event.data);
  
  // 检查是否有pluginMessage包装
  const message = event.data.pluginMessage || event.data;
  console.log('[Spine2D UI] Processed message:', message);
  
  if (message.type === 'export-complete') {
    console.log('[Spine2D UI] Export completed, processing data');
    handleExportComplete(message.data);
  } else if (message.type === 'error') {
    console.log('[Spine2D UI] Error received:', message.message);
    showStatus(message.message, 'error');
    exportBtn.disabled = false;
  } else {
    console.log('[Spine2D UI] Unknown message type:', message.type);
  }
};

// 添加调试信息
console.log('[Spine2D UI] Window object:', typeof window, window.location ? window.location.href : 'no location');
console.log('[Spine2D UI] Parent object:', typeof parent, parent === window ? 'parent is window' : 'parent is not window');

// 添加更多调试信息
try {
  console.log('[Spine2D UI] Window parent relationship:', {
    'window === parent': window === parent,
    'window === self': window === self,
    'parent === self': parent === self,
    'window.parent === parent': window.parent === parent,
  });
} catch (error) {
  console.error('[Spine2D UI] Error checking window relationships:', error);
}


// Handle successful export
function handleExportComplete(exportData: Array<{ name: string; data: any }>) {
  try {
    // Download each JSON file
    exportData.forEach(({ name, data }) => {
      const jsonString = JSON.stringify(data, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `${name}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);
    });
    
    const fileCount = exportData.length;
    const message = fileCount === 1 
      ? `成功导出 1 个文件` 
      : `成功导出 ${fileCount} 个文件`;
    
    showStatus(message, 'success');
    
  } catch (error) {
    showStatus('下载文件时出错', 'error');
  }
  
  exportBtn.disabled = false;
}

// Show status message
function showStatus(message: string, type: 'success' | 'error' | 'info', showLoading = false) {
  statusDiv.className = `status ${type}`;
  statusDiv.style.display = 'block';
  
  if (showLoading) {
    statusDiv.innerHTML = `<span class="loading"></span>${message}`;
  } else {
    statusDiv.textContent = message;
  }
  
  // Auto-hide success messages after 3 seconds
  if (type === 'success') {
    setTimeout(() => {
      statusDiv.style.display = 'none';
    }, 3000);
  }
}