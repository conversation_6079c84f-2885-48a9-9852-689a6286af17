<!doctype html><html><head><meta charset="utf-8"><title>Figma to Spine2D Exporter</title><script>console.log('[Spine2D UI] HTML加载');
    
    // 添加DOM加载完成后的调试信息
    document.addEventListener('DOMContentLoaded', function() {
      console.log('[Spine2D UI] DOM完全加载');
      
      // 检查按钮是否存在
      const exportBtn = document.getElementById('export-btn');
      console.log('[Spine2D UI] 导出按钮元素:', exportBtn);
      
      if (exportBtn) {
        console.log('[Spine2D UI] 导出按钮已找到，添加点击事件监听器');
      } else {
        console.error('[Spine2D UI] 错误: 未找到导出按钮元素!');
      }
    });</script><style>body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background: #ffffff;
      color: #333;
    }
    
    .container {
      max-width: 360px;
      margin: 0 auto;
    }
    
    h1 {
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 16px 0;
      color: #1a1a1a;
    }
    
    .description {
      font-size: 13px;
      color: #666;
      margin-bottom: 20px;
      line-height: 1.4;
    }
    
    .button {
      width: 100%;
      padding: 12px 16px;
      background: #18a0fb;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    .button:hover {
      background: #0d8ce8;
    }
    
    .button:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
    
    .status {
      margin-top: 16px;
      padding: 12px;
      border-radius: 6px;
      font-size: 13px;
      display: none;
    }
    
    .status.success {
      background: #e8f5e8;
      color: #2d5a2d;
      border: 1px solid #c3e6c3;
    }
    
    .status.error {
      background: #ffeaea;
      color: #d93025;
      border: 1px solid #f5c6cb;
    }
    
    .status.info {
      background: #e3f2fd;
      color: #1565c0;
      border: 1px solid #bbdefb;
    }
    
    .loading {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #18a0fb;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 8px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }</style><script defer="defer" src="ui.js"></script></head><body><div class="container"><h1>Spine2D 导出器</h1><div class="description">选择画板或框架，然后点击导出按钮生成 Spine2D 兼容的 JSON 文件。</div><button id="export-btn" class="button">导出到 Spine2D</button><div id="status" class="status"></div></div></body></html>