// Figma plugin main code

// 显示插件UI
figma.showUI(__html__, { width: 300, height: 200 });
console.log('[Figma Plugin] 插件已加载，UI已显示');
console.log('[Figma Plugin] 当前Figma API版本:', figma.apiVersion);
console.log('[Figma Plugin] 当前文档ID:', figma.fileKey);
console.log('[Figma Plugin] 当前页面:', figma.currentPage.name);
console.log('[Figma Plugin] figma对象:', figma);
console.log('[Figma Plugin] figma.ui对象:', figma.ui);

// 添加更多调试信息
console.log('[Figma Plugin] figma.ui.onmessage类型:', typeof figma.ui.onmessage);
console.log('[Figma Plugin] figma.ui.postMessage类型:', typeof figma.ui.postMessage);

// 测试消息发送
try {
  console.log('[Figma Plugin] 尝试发送测试消息到UI');
  figma.ui.postMessage({ type: 'test', message: '这是一条测试消息' });
  console.log('[Figma Plugin] 测试消息已发送');
} catch (error) {
  console.error('[Figma Plugin] 发送测试消息时出错:', error);
}

// 监听来自UI的消息
figma.ui.onmessage = async (msg) => {
  console.log('[Figma Plugin] 收到消息:', msg);
  console.log('[Figma Plugin] 消息类型:', typeof msg, '内容:', JSON.stringify(msg));

  if (msg.type === 'export-spine2d') {
    try {
      console.log('[Figma Plugin] 处理选择...');
      const selection = figma.currentPage.selection;
      
      if (selection.length === 0) {
        console.log('[Figma Plugin] 未找到选择');
        figma.ui.postMessage({ 
          type: 'error', 
          message: '请选择要导出的元素' 
        });
        return;
      }
      
      // 只处理第一个选中的元素
      const selectedNode = selection[0];
      console.log('[Figma Plugin] 选中节点类型:', selectedNode.type, '名称:', selectedNode.name);
      
      // 检查是否为Frame类型
      if (selectedNode.type === 'FRAME') {
        const frameNode = selectedNode as FrameNode;
        console.log('[Figma Plugin] 开始转换Frame为JSON...');
        const jsonData = await convertFrameToSpine2D(frameNode);
        
        console.log('[Figma Plugin] 导出完成');
        figma.ui.postMessage({ 
          type: 'export-complete', 
          data: [{
            name: frameNode.name,
            data: jsonData
          }]
        });
      } else {
        console.log('[Figma Plugin] 不支持的节点类型:', selectedNode.type);
        figma.ui.postMessage({ 
          type: 'error', 
          message: `目前只支持导出Frame类型，您选择的是 ${selectedNode.type} 类型。` 
        });
      }
      
    } catch (error) {
      console.error('[Figma Plugin] 导出错误:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      figma.ui.postMessage({ 
        type: 'error', 
        message: `导出失败: ${errorMessage}` 
      });
    }
  }
};

// 定义元素信息的接口
interface ElementInfo {
  name: string;
  type: string;
  x: number;
  y: number;
  width: number;
  height: number;
  centerX: number;
  centerY: number;
}

// 将Frame转换为简单的JSON格式，只包含名称和位置信息
async function convertFrameToSpine2D(frameNode: FrameNode): Promise<any> {
  console.log('[Figma Plugin] 开始转换Frame:', frameNode.name);
  
  // 创建基本的JSON数据结构
  const jsonData = {
    name: frameNode.name,
    type: frameNode.type,
    width: frameNode.width,
    height: frameNode.height,
    elements: [] as ElementInfo[]
  };
  
  // 获取Frame的边界
  const frameBounds = {
    x: frameNode.x,
    y: frameNode.y,
    width: frameNode.width,
    height: frameNode.height
  };
  
  console.log('[Figma Plugin] Frame边界:', frameBounds);
  
  // 处理Frame内的直接子元素
  const childNodes = frameNode.children;
  console.log(`[Figma Plugin] 处理 ${childNodes.length} 个子节点`);
  
  // 收集所有子元素的信息
  for (let i = 0; i < childNodes.length; i++) {
    const node = childNodes[i];
    console.log(`[Figma Plugin] 处理节点: ${node.name} (${node.type})`);
    
    // 计算节点相对于Frame的位置
    const x = node.x - frameBounds.x;
    const y = node.y - frameBounds.y;
    const centerX = x + node.width / 2;
    const centerY = y + node.height / 2;
    
    // 添加到元素列表
    jsonData.elements.push({
      name: node.name,
      type: node.type,
      x: x,
      y: y,
      width: node.width,
      height: node.height,
      centerX: centerX,
      centerY: centerY
    });
  }
  
  return jsonData;
}



// 不再需要检查嵌套元素，因为我们现在允许任何结构