{"name": "tad-figma-text-lint", "version": "1.0.0", "description": "Your Figma Plugin", "main": "code.js", "scripts": {"build": "webpack --mode=production", "start": "webpack --mode=development --watch", "lint:fix": "eslint --ext .ts .js .tsx .jsx --fix src/** --no-error-on-unmatched-pattern", "lint": "eslint --ext .ts .js .tsx .jsx src/** --no-error-on-unmatched-pattern", "docs:dev": "vuepress dev docs", "docs:build": "vuepress build docs", "lint:css": "stylelint **/*.{html,vue,css,sass,scss,less}"}, "lint-staged": {"*.{html,vue,css,sass,scss,less}": ["stylelint --fix", "git add"]}, "author": "qefeng", "license": "ISC", "devDependencies": {"@tencent/eslint-config-tencent": "^0.15.2", "@tencent/stylelint-config-tencent": "^1.0.7", "@types/crypto-js": "^4.0.1", "@types/figma": "^1.0.3", "@types/lodash": "^4.14.161", "@types/node": "^14.14.5", "@types/react": "^16.9.49", "@types/react-dom": "^16.9.8", "@types/react-router-dom": "^5.1.5", "@types/react-syntax-highlighter": "^13.5.0", "@types/tinycolor2": "^1.4.2", "@typescript-eslint/eslint-plugin": "^4.6.0", "@typescript-eslint/parser": "^4.6.0", "css-loader": "^3.1.0", "eslint": "^7.32.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "html-webpack-inline-source-plugin": "0.0.10", "html-webpack-plugin": "^3.2.0", "markdown-it-task-lists": "^2.1.1", "prettier": "2.1.2", "sass": "^1.32.0", "sass-loader": "^10.1.0", "style-loader": "^0.23.1", "stylelint": "^13.13.1", "ts-loader": "^6.0.4", "typescript": "^3.9.7", "url-loader": "^2.1.0", "vuepress": "^1.8.0", "vuepress-bar": "^0.3.2", "webpack": "^4.44.1", "webpack-cli": "^3.3.6"}, "dependencies": {"@octokit/core": "^3.2.4", "@types/fabric": "^3.6.9", "ini": "^2.0.0", "js-base64": "^3.6.0", "lodash": "^4.17.21", "parse-github-url": "^1.0.2", "react": "^16.13.1", "react-dom": "^16.13.1", "react-router-dom": "^5.2.0", "react-syntax-highlighter": "^15.4.3", "tinycolor2": "^1.4.2"}}